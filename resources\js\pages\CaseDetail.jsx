import React, { useState, useRef, useEffect } from "react";
import ProjectShowcase from "../components/ProjectShowcase";
import SeoHead from "../components/SeoHead";
import { CaretLeftIcon, CaretRightIcon, XIcon } from "@phosphor-icons/react";

const CaseDetail = ({ caseDetail = caseDetail, cases = [], seoData }) => {
    // Carousel modal state
    const [carouselOpen, setCarouselOpen] = useState(false);
    const [carouselIdx, setCarouselIdx] = useState(0);
    const [showControls, setShowControls] = useState(true);
    const [isTouchDevice, setIsTouchDevice] = useState(false);
    // Detect touch device on mount
    useEffect(() => {
        const checkTouch = () => {
            setIsTouchDevice(
                "ontouchstart" in window ||
                    navigator.maxTouchPoints > 0 ||
                    navigator.msMaxTouchPoints > 0,
            );
        };
        checkTouch();
    }, []);
    const carouselRef = useRef(null);
    const mouseInWindow = useRef(true);
    const touchStartX = useRef(null);

    // Open carousel at image idx
    const openCarousel = (idx) => {
        setCarouselIdx(idx);
        setCarouselOpen(true);
        setShowControls(true);
    };

    // Close carousel
    const closeCarousel = () => {
        setCarouselOpen(false);
    };

    // Keyboard navigation
    useEffect(() => {
        if (!carouselOpen) return;
        const handleKey = (e) => {
            if (e.key === "Escape") closeCarousel();
            if (e.key === "ArrowLeft" && carouselIdx > 0)
                setCarouselIdx((i) => i - 1);
            if (
                e.key === "ArrowRight" &&
                carouselIdx < caseDetail.images.length - 1
            )
                setCarouselIdx((i) => i + 1);
        };
        window.addEventListener("keydown", handleKey);
        return () => window.removeEventListener("keydown", handleKey);
    }, [carouselOpen, carouselIdx, caseDetail.images.length]);

    // Mouse enter/leave viewport for controls (only for non-touch devices)
    useEffect(() => {
        if (!carouselOpen || isTouchDevice) return;
        let fadeTimeout = null;
        const handleMouse = (e) => {
            if (e.type === "mouseout" && !e.relatedTarget && !e.toElement) {
                mouseInWindow.current = false;
                fadeTimeout = setTimeout(() => {
                    setShowControls(false);
                }, 200);
            } else if (e.type === "mouseover") {
                setShowControls(true);
                mouseInWindow.current = true;
                if (fadeTimeout) clearTimeout(fadeTimeout);
            }
        };
        window.addEventListener("mouseout", handleMouse);
        window.addEventListener("mouseover", handleMouse);
        return () => {
            window.removeEventListener("mouseout", handleMouse);
            window.removeEventListener("mouseover", handleMouse);
            if (fadeTimeout) clearTimeout(fadeTimeout);
        };
    }, [carouselOpen, isTouchDevice]);

    // Touch/swipe support
    const handleTouchStart = (e) => {
        if (e.touches.length === 1) {
            touchStartX.current = e.touches[0].clientX;
        }
    };
    const handleTouchEnd = (e) => {
        if (touchStartX.current === null) return;
        const touchEndX = e.changedTouches[0].clientX;
        const dx = touchEndX - touchStartX.current;
        if (Math.abs(dx) > 50) {
            if (dx > 0 && carouselIdx > 0) setCarouselIdx((i) => i - 1);
            if (dx < 0 && carouselIdx < caseDetail.images.length - 1)
                setCarouselIdx((i) => i + 1);
        }
        touchStartX.current = null;
    };

    // Carousel modal JSX
    const CarouselModal = () => {
        // Controls use mouse logic for fade on non-touch devices
        const controlsShouldShow = showControls;
        return (
            <div
                ref={carouselRef}
                className="bg-opacity-100 fixed inset-0 z-50 flex items-center justify-center bg-black transition-all duration-300"
                style={{
                    minHeight: "100vh",
                    minWidth: "100vw",
                }}
                onTouchStart={handleTouchStart}
                onTouchEnd={handleTouchEnd}
            >
                {/* Controls wrapper with fade transition */}
                <div
                    className="pointer-events-none fixed inset-0 z-50"
                    style={{
                        opacity: controlsShouldShow ? 1 : 0,
                        transition: "opacity 300ms ease-in-out",
                        willChange: "opacity",
                    }}
                >
                    {/* Close button */}
                    <button
                        aria-label="Close"
                        onClick={closeCarousel}
                        className="pointer-events-auto absolute top-6 right-8 cursor-pointer rounded-full bg-[rgb(52,52,52)] p-3 text-3xl text-white transition-colors duration-150 hover:bg-[rgb(105,105,105)] focus:outline-none"
                        style={{ zIndex: 20 }}
                    >
                        <XIcon size={16} weight="bold" />
                    </button>
                    {/* Left arrow - hidden on touch devices */}
                    {!isTouchDevice && carouselIdx > 0 && (
                        <button
                            aria-label="Previous image"
                            onClick={() => setCarouselIdx((i) => i - 1)}
                            className="pointer-events-auto absolute top-1/2 left-8 -translate-y-1/2 cursor-pointer rounded-full bg-[rgb(52,52,52)] p-3 text-5xl font-light text-white transition-colors duration-150 hover:bg-[rgb(105,105,105)] focus:outline-none"
                            style={{ zIndex: 20 }}
                        >
                            <span>
                                <CaretLeftIcon weight="bold" size={16} />
                            </span>
                        </button>
                    )}
                    {/* Right arrow - hidden on touch devices */}
                    {!isTouchDevice &&
                        carouselIdx < caseDetail.images.length - 1 && (
                            <button
                                aria-label="Next image"
                                onClick={() => setCarouselIdx((i) => i + 1)}
                                className="pointer-events-auto absolute top-1/2 right-8 -translate-y-1/2 cursor-pointer rounded-full bg-[rgb(52,52,52)] p-3 text-5xl font-light text-white transition-colors duration-150 hover:bg-[rgb(105,105,105)] focus:outline-none"
                                style={{ zIndex: 20 }}
                            >
                                <span>
                                    <CaretRightIcon weight="bold" size={16} />
                                </span>
                            </button>
                        )}
                    {/* Footer info */}
                    <div
                        className="pointer-events-auto absolute bottom-0 left-0 flex w-full cursor-pointer items-end bg-gradient-to-t from-black/60 to-transparent px-8 pt-10 pb-6"
                        style={{ minHeight: 90 }}
                    >
                        {/* Logo */}
                        <div className="my-auto mr-4 flex items-center">
                            <img
                                src={caseDetail.logo}
                                alt={caseDetail.companyName}
                                className="h-14 w-14 rounded-xs object-cover shadow-lg"
                                style={{ minWidth: 56, minHeight: 56 }}
                            />
                        </div>
                        {/* Project name */}
                        <div
                            className="my-auto flex flex-col justify-center text-base font-bold text-white"
                            style={{ minWidth: 180 }}
                        >
                            <span className="text-lg font-semibold tracking-wide">
                                {caseDetail.companyName}
                            </span>
                            <span className="mt-1 text-xs font-light opacity-80">
                                {caseDetail.projectName}
                            </span>
                        </div>
                    </div>
                </div>
                {/* Centered image */}
                <div
                    className="flex h-full w-full flex-col items-center justify-center"
                    style={{ minHeight: "100vh", minWidth: "100vw" }}
                >
                    <img
                        src={caseDetail.images[carouselIdx]}
                        alt={`Case image ${carouselIdx + 1}`}
                        className="aspect-square size-full object-contain shadow-none transition-all duration-300"
                        style={{
                            background: "transparent",
                            margin: "0 auto",
                        }}
                    />
                </div>
            </div>
        );
    };

    return (
        <React.Fragment>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            {/* Carousel Modal */}
            {carouselOpen && <CarouselModal />}
            <div className="mx-auto flex min-h-screen w-full max-w-5xl flex-col items-center bg-white">
                {/* Content Container - matches project images width */}
                <div className="w-full max-w-6xl">
                    {/* Header - left aligned within project images width */}
                    <div className="flex w-fit items-center space-x-4 px-2 pb-6 lg:px-0">
                        {/* Logo circle */}
                        <div className="flex size-10 items-center justify-center rounded-[50%] bg-transparent shadow-lg">
                            <img
                                src={caseDetail.logo}
                                alt={caseDetail.companyName}
                                className="size-10 rounded-[50%] object-contain"
                            />
                        </div>
                        {/* Text block */}
                        <div>
                            <h1 className="text-base leading-none font-bold text-gray-900">
                                {caseDetail.companyName}
                            </h1>
                            <p className="mt-1.5 text-sm leading-none text-gray-600">
                                {caseDetail.projectName}
                            </p>
                        </div>
                    </div>
                    {/* Image Stack - no gaps */}
                    <div
                        className="flex flex-col"
                        style={{ fontSize: 0, lineHeight: 0 }}
                    >
                        {caseDetail.images.map((img, idx) => (
                            <img
                                key={idx}
                                src={img}
                                alt={`Case image ${idx + 1}`}
                                className="w-full cursor-zoom-in object-contain"
                                style={{
                                    display: "block",
                                    margin: 0,
                                    padding: 0,
                                    border: 0,
                                    verticalAlign: "top",
                                }}
                                onClick={() => openCarousel(idx)}
                            />
                        ))}
                    </div>
                </div>
                {/* More Cases Section */}
                <div className="mx-auto mt-12 w-full max-w-6xl">
                    <h2 className="mb-6 text-2xl font-bold text-[#2d2318]">
                        More cases
                    </h2>
                    <ProjectShowcase cases={cases} />
                </div>
            </div>
        </React.Fragment>
    );
};

export default CaseDetail;
