import { createInertiaApp } from "@inertiajs/react";
import { createRoot } from "react-dom/client";
import "../css/app.css";
import Layout from "./Layout";
import AdminLayout from "./AdminLayout";

createInertiaApp({
    progress: {
        color: "#ff6400"
    },
    resolve: (name) => {
        const pages = import.meta.glob("./pages/**/*.jsx", { eager: true });
        const page = pages[`./pages/${name}.jsx`];

        // Determine if this is an admin route (but exclude login page)
        const isAdminRoute =
            name.startsWith("Admin/") && name !== "Admin/Login";
        const isAdminLogin = name === "Admin/Login";

        // Set the appropriate layout based on the route
        page.default.layout =
            page.default.layout ||
            ((page) => {
                if (isAdminRoute) {
                    return <AdminLayout>{page}</AdminLayout>;
                } else if (isAdminLogin) {
                    return page; // No layout for admin login
                } else {
                    return <Layout>{page}</Layout>;
                }
            });

        return page;
    },
    setup({ el, App, props }) {
        createRoot(el).render(<App {...props} />);
    },
});
