@import url("https://fonts.googleapis.com/css2?family=Great+Vibes&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import "tailwindcss";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --breakpoint-xs: 31.375rem; /* 502px */
    --breakpoint-tablet: 50.625rem; /* 810px */
    --breakpoint-desktop: 80rem; /* 1280px */
}

body {
    font-family: "Roboto", sans-serif;
}

@utility font-greatvibes {
    font-family: "Great Vibes", cursive;
}

/* Screen Reader Only - Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Enhanced ScrollReveal Animations - Hardware Accelerated */
@layer utilities {
    /* Base animation utilities with hardware acceleration */
    .scroll-reveal-base {
        will-change: transform, opacity;
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
    }

    /* Performance optimized transforms */
    .gpu-accelerated {
        transform: translateZ(0);
        will-change: transform, opacity;
    }

    /* Smooth animation curves */
    .ease-reveal {
        transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .ease-reveal-fast {
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }

    .ease-reveal-slow {
        transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    /* Disable ScrollReveal animations for users who prefer reduced motion */
    .scroll-reveal-base {
        will-change: auto;
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .scroll-reveal-base {
        /* Ensure animations don't interfere with high contrast */
        filter: none;
    }
}

/* Performance optimizations for lower-end devices */
@media (max-width: 768px) {
    .scroll-reveal-base {
        /* Reduce animation complexity on mobile */
        will-change: opacity;
    }
}

/* Dark mode considerations */
@media (prefers-color-scheme: dark) {
    .scroll-reveal-base {
        /* Ensure animations work well in dark mode */
        color-scheme: dark;
    }
}
/* Inertia.js Progress Indicator */
.inertia-progress {
    background-color: #ff6400 !important;
}

/* Additional progress bar selectors for complete coverage */
#nprogress .bar {
    background: #ff6400 !important;
}

#nprogress .peg {
    box-shadow:
        0 0 10px #ff6400,
        0 0 5px #ff6400 !important;
}

#nprogress .spinner-icon {
    border-top-color: #ff6400 !important;
    border-left-color: #ff6400 !important;
}

@keyframes logo-slider {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}
.animate-logo-slider {
    animation: logo-slider 18s linear infinite;
    width: max-content;
    display: flex;
}
.animate-logo-slider:hover {
    animation-play-state: paused;
}
